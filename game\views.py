from django.shortcuts import render, redirect
from django.http import JsonResponse, HttpResponseRedirect
from django.views.decorators.csrf import csrf_exempt, ensure_csrf_cookie
from django.conf import settings
from django.utils import timezone
import json
import logging
import sys
import uuid
import datetime
import markdown
import os

# Import models at the module level to ensure they're available in all functions
from .models import GameSession, Message, CompanyGameSettings
from corporate.models import Company
from .characters import CHARACTERS
from .role_progression import ROLE_PROGRESSION
from .all_role_tasks import get_all_role_tasks
from .role_tasks import generate_role_progression_html # Added this import
from .game_state_manager import (
    check_for_promotion,
    process_task_completion,
    process_task_failure,
    get_performance_summary
)

# Try to import enhanced evaluation module
try:
    from .enhanced_evaluation import (
        evaluate_response_with_enhanced_llm,
        calculate_score,
        generate_improvement_feedback
    )
    ENHANCED_EVALUATION_AVAILABLE = True
    logging.info("Enhanced evaluation module loaded successfully")
except ImportError as e:
    logging.warning(f"Enhanced evaluation module not available: {str(e)}. Falling back to basic evaluation.")
    ENHANCED_EVALUATION_AVAILABLE = False

# Try to import prompt evaluation module
try:
    from .prompt_evaluation import evaluate_prompt
    PROMPT_EVALUATION_AVAILABLE = True
    logging.info("Prompt evaluation module loaded successfully")
except ImportError as e:
    logging.warning(f"Prompt evaluation module not available: {str(e)}. Falling back to basic evaluation.")
    PROMPT_EVALUATION_AVAILABLE = False

# Try to import LLM feedback generator
try:
    from .llm_feedback_generator import generate_manager_feedback as llm_generate_manager_feedback
    LLM_FEEDBACK_GENERATOR_AVAILABLE = True
    logging.info("LLM feedback generator module loaded successfully")
except ImportError as e:
    logging.warning(f"LLM feedback generator module not available: {str(e)}. Using template feedback.")
    LLM_FEEDBACK_GENERATOR_AVAILABLE = False

# Try to import LLM response generator
try:
    from .llm_response_generator_openai_format import generate_response_with_llm
    LLM_RESPONSE_AVAILABLE = True
    logging.info("LLM response generator loaded successfully")
except ImportError as e:
    logging.warning(f"LLM response generator not available: {str(e)}. Using template responses.")
    LLM_RESPONSE_AVAILABLE = False

# Try to import org chart generator
try:
    from .generate_org_chart import generate_org_chart_html
    ORG_CHART_AVAILABLE = True
    logging.info("Org chart generator loaded successfully")
except ImportError as e:
    logging.warning(f"Org chart generator not available: {str(e)}. Using placeholder.")
    ORG_CHART_AVAILABLE = False

    def generate_org_chart_html(current_role, completed_roles):
        return "<div class='org-chart'>Organization Chart Placeholder</div>"

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s', stream=sys.stdout)

def help_redirect(request):
    """
    Redirects to the help URL configured in the CompanyGameSettings.
    If no company is active or no help URL is configured, redirects to a default help URL.
    """
    # Get the active company from the session
    active_company_id = request.session.get('active_company_id')
    default_help_url = "https://www.example.com/help"  # Default help URL

    if active_company_id:
        try:
            from corporate.models import Company
            company = Company.objects.get(id=active_company_id)

            # Get the company game settings
            try:
                company_settings = CompanyGameSettings.objects.get(company=company)
                if company_settings.help_url:
                    return HttpResponseRedirect(company_settings.help_url)
            except CompanyGameSettings.DoesNotExist:
                pass
        except Exception as e:
            logging.error(f"Error in help_redirect: {str(e)}")

    # If no company or no help URL configured, use default
    return HttpResponseRedirect(default_help_url)

def migrate_anonymous_session(session_id, user):
    """
    Migrate an anonymous session to an authenticated user.
    This allows users to continue their progress after logging in.

    Args:
        session_id: The anonymous session ID
        user: The authenticated user

    Returns:
        The migrated GameSession or None if no session was found
    """
    try:
        # Find the anonymous session
        anonymous_session = GameSession.objects.filter(
            session_id=session_id,
            user__isnull=True
        ).first()

        if not anonymous_session:
            return None

        # Check if the user already has a session
        existing_user_session = GameSession.objects.filter(
            user=user
        ).first()

        if existing_user_session:
            # If the anonymous session has more progress, update the user session
            if anonymous_session.performance_score > existing_user_session.performance_score:
                # Copy progress from anonymous session to user session
                existing_user_session.current_role = anonymous_session.current_role
                existing_user_session.performance_score = anonymous_session.performance_score
                existing_user_session.challenges_completed = anonymous_session.challenges_completed
                existing_user_session.role_challenges_completed = anonymous_session.role_challenges_completed
                existing_user_session.game_completed = anonymous_session.game_completed
                existing_user_session.current_manager = anonymous_session.current_manager
                existing_user_session.current_task = anonymous_session.current_task
                existing_user_session.completed_roles = anonymous_session.completed_roles
                existing_user_session.first_task_pending = anonymous_session.first_task_pending
                existing_user_session.next_task_pending = anonymous_session.next_task_pending
                existing_user_session.save()

                # Copy messages from anonymous session to user session
                for message in anonymous_session.messages.all():
                    # Check if this message already exists in the user session
                    if not existing_user_session.messages.filter(message_id=message.message_id).exists():
                        message.pk = None  # Create a new object
                        message.game_session = existing_user_session
                        message.save()

                # Ensure the welcome message exists and is the first message
                if not existing_user_session.messages.filter(sender="hr", is_challenge=False).exists():
                    # Add welcome message
                    company_name = "Rwenzori Innovations Ltd."
                    if existing_user_session.company:
                        company_name = existing_user_session.company.name

                    # Check if there's a custom welcome message in company settings
                    custom_welcome = ""
                    if existing_user_session.company:
                        try:
                            settings = CompanyGameSettings.objects.get(company=existing_user_session.company)
                            if settings.custom_welcome_message:
                                custom_welcome = f"\n\n{settings.custom_welcome_message}"
                        except Exception as e:
                            logging.error(f"Error getting company welcome message: {str(e)}")

                    welcome_text = f'''# Welcome to {company_name}

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.{custom_welcome}'''
                    welcome_html = markdown.markdown(welcome_text, extensions=['extra'])

                    # Create welcome message with a specific message_id to make it identifiable
                    welcome_message_id = str(uuid.uuid4())
                    welcome_message = Message.objects.create(
                        game_session=existing_user_session,
                        message_id=welcome_message_id,
                        sender="hr",
                        text=welcome_text,
                        html=welcome_html,
                        is_challenge=False,
                        is_markdown=True,
                        # Set timestamp to a slightly earlier time to ensure it appears first
                        timestamp=timezone.now() - datetime.timedelta(seconds=10)
                    )
                    logging.info(f"Created welcome message with ID: {welcome_message_id} and database ID: {welcome_message.id} during session migration")

                # Delete the anonymous session
                anonymous_session.delete()
                return existing_user_session
            else:
                # User session has more progress, just delete the anonymous session
                anonymous_session.delete()
                return existing_user_session
        else:
            # User doesn't have a session, convert the anonymous session
            anonymous_session.user = user
            anonymous_session.session_id = None
            anonymous_session.save()

            # Ensure the welcome message exists and is the first message
            if not anonymous_session.messages.filter(sender="hr", is_challenge=False).exists():
                # Add welcome message
                company_name = "Rwenzori Innovations Ltd."
                if anonymous_session.company:
                    company_name = anonymous_session.company.name

                # Check if there's a custom welcome message in company settings
                custom_welcome = ""
                if anonymous_session.company:
                    try:
                        settings = CompanyGameSettings.objects.get(company=anonymous_session.company)
                        if settings.custom_welcome_message:
                            custom_welcome = f"\n\n{settings.custom_welcome_message}"
                    except Exception as e:
                        logging.error(f"Error getting company welcome message: {str(e)}")

                welcome_text = f'''# Welcome to {company_name}

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.{custom_welcome}'''
                welcome_html = markdown.markdown(welcome_text, extensions=['extra'])

                # Create welcome message with a specific message_id to make it identifiable
                welcome_message_id = str(uuid.uuid4())
                welcome_message = Message.objects.create(
                    game_session=anonymous_session,
                    message_id=welcome_message_id,
                    sender="hr",
                    text=welcome_text,
                    html=welcome_html,
                    is_challenge=False,
                    is_markdown=True,
                    # Set timestamp to a slightly earlier time to ensure it appears first
                    timestamp=timezone.now() - datetime.timedelta(seconds=10)
                )
                logging.info(f"Created welcome message with ID: {welcome_message_id} and database ID: {welcome_message.id} during anonymous session conversion")

            return anonymous_session
    except Exception as e:
        logging.error(f"Error migrating anonymous session: {str(e)}")
        return None


def get_or_create_game_session(request):
    """
    Get or create a game session for the current user/session
    """
    try:
        # Get company from request if available
        company_id = request.session.get('active_company_id')
        company = None
        if company_id:
            try:
                # Company is already imported at the module level
                company = Company.objects.get(id=company_id)
                logging.info(f"Found company {company.name} (ID: {company.id}) from active_company_id")
            except (Company.DoesNotExist, ValueError):
                logging.warning(f"Company with ID {company_id} not found")
                company = None

        # If user is authenticated, try to get their corporate profile's company
        if request.user.is_authenticated and not company:
            try:
                # Import here to avoid circular imports
                from corporate.models import CorporateUser

                # Try to get the user's corporate profile
                try:
                    corporate_profile = CorporateUser.objects.get(user=request.user)
                    corporate_company = corporate_profile.company

                    # Log the corporate company information
                    logging.info(f"Found corporate profile for user {request.user.username} with company {corporate_company.name} (ID: {corporate_company.id})")

                    # Use the corporate company directly
                    company = corporate_company
                    logging.info(f"Using corporate company {company.name} (ID: {company.id}) directly")

                    # Update the session
                    request.session['active_company_id'] = str(company.id)
                except CorporateUser.DoesNotExist:
                    logging.warning(f"No corporate profile found for user {request.user.username}")
            except Exception as e:
                logging.error(f"Error getting corporate profile company: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())

        # Get team from request if available
        team = request.GET.get('team', '')

        # Check if user is authenticated
        if request.user.is_authenticated:
            # Get the session ID before we potentially create a user session
            session_id = request.session.session_key

            # Try to get existing game session for this user
            try:
                game_session = GameSession.objects.get(user=request.user)
                created = False

                # If the session exists but doesn't have a company, update it
                if not game_session.company and company:
                    game_session.company = company
                    game_session.team = team
                    game_session.save(update_fields=['company', 'team'])
                    logging.info(f"Updated existing game session with company {company.name} for user {request.user.username}")
            except GameSession.DoesNotExist:
                # Create a new game session
                game_session = GameSession.objects.create(
                    user=request.user,
                    company=company,
                    team=team
                )
                created = True
                logging.info(f"Created new game session with company {company.name if company else 'None'} for user {request.user.username}")

            # If we just created a new session and there's a session ID,
            # check if there's an anonymous session to migrate
            if created and session_id:
                migrated_session = migrate_anonymous_session(session_id, request.user)
                if migrated_session:
                    game_session = migrated_session

                    # Update company and team if they were provided
                    if company and not game_session.company:
                        game_session.company = company
                    if team and not game_session.team:
                        game_session.team = team
                    game_session.save()

            # If this is a newly created session, add the welcome message
            if created and not game_session.messages.filter(sender="hr", is_challenge=False).exists():
                # Add welcome message
                company_name = "Rwenzori Innovations Ltd."
                if game_session.company:
                    company_name = game_session.company.name

                # Check if there's a custom welcome message in company settings
                custom_welcome = ""
                if game_session.company:
                    try:
                        settings = CompanyGameSettings.objects.get(company=game_session.company)
                        if settings.custom_welcome_message:
                            custom_welcome = f"\n\n{settings.custom_welcome_message}"
                    except Exception as e:
                        logging.error(f"Error getting company welcome message: {str(e)}")

                welcome_text = f'''# Welcome to {company_name}

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.{custom_welcome}'''
                welcome_html = markdown.markdown(welcome_text, extensions=['extra'])

                # Create welcome message with a specific message_id to make it identifiable
                welcome_message_id = str(uuid.uuid4())
                welcome_message = Message.objects.create(
                    game_session=game_session,
                    message_id=welcome_message_id,
                    sender="hr",
                    text=welcome_text,
                    html=welcome_html,
                    is_challenge=False,
                    is_markdown=True,
                    # Set timestamp to a slightly earlier time to ensure it appears first
                    timestamp=timezone.now() - datetime.timedelta(seconds=10)
                )
                logging.info(f"Created welcome message with ID: {welcome_message_id} and database ID: {welcome_message.id} for new user")
        else:
            # For anonymous users, use session ID
            session_id = request.session.session_key
            if not session_id:
                # Create a new session if one doesn't exist
                request.session.create()
                session_id = request.session.session_key

            # Try to get existing game session for this session ID
            game_session, created = GameSession.objects.get_or_create(
                session_id=session_id,
                defaults={
                    'company': company,
                    'team': team
                }
            )

            # If this is a newly created session, add the welcome message
            if created and not game_session.messages.filter(sender="hr", is_challenge=False).exists():
                # Add welcome message
                company_name = "Rwenzori Innovations Ltd."
                if game_session.company:
                    company_name = game_session.company.name

                # Check if there's a custom welcome message in company settings
                custom_welcome = ""
                if game_session.company:
                    try:
                        settings = CompanyGameSettings.objects.get(company=game_session.company)
                        if settings.custom_welcome_message:
                            custom_welcome = f"\n\n{settings.custom_welcome_message}"
                    except Exception as e:
                        logging.error(f"Error getting company welcome message: {str(e)}")

                welcome_text = f'''# Welcome to {company_name}

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.{custom_welcome}'''
                welcome_html = markdown.markdown(welcome_text, extensions=['extra'])

                # Create welcome message with a specific message_id to make it identifiable
                welcome_message_id = str(uuid.uuid4())
                welcome_message = Message.objects.create(
                    game_session=game_session,
                    message_id=welcome_message_id,
                    sender="hr",
                    text=welcome_text,
                    html=welcome_html,
                    is_challenge=False,
                    is_markdown=True,
                    # Set timestamp to a slightly earlier time to ensure it appears first
                    timestamp=timezone.now() - datetime.timedelta(seconds=10)
                )
                logging.info(f"Created welcome message with ID: {welcome_message_id} and database ID: {welcome_message.id} for anonymous user")

        # BUGFIX: Add next_task_pending attribute if it doesn't exist
        # This is a temporary fix until the migration is properly applied
        if not hasattr(game_session, 'next_task_pending'):
            logging.warning("next_task_pending attribute missing, adding it dynamically")
            game_session.next_task_pending = False

        return game_session

    except Exception as e:
        # If there's an error (like missing column), create a new session object without saving it
        logging.error(f"Error getting game session: {str(e)}")
        logging.info("Creating temporary in-memory game session")

        # Create a new GameSession object without saving it to the database
        game_session = GameSession()

        # Set default values
        game_session.current_role = "applicant"
        game_session.performance_score = 0
        game_session.challenges_completed = 0
        game_session.role_challenges_completed = 0
        game_session.game_completed = False
        game_session.current_manager = "hr"
        game_session.current_task = "cover_letter"
        game_session.completed_roles = "[]"
        game_session.first_task_pending = True
        game_session.next_task_pending = False

        # If user is authenticated, set the user
        if request.user.is_authenticated:
            game_session.user = request.user
        else:
            # Otherwise use session ID
            session_id = request.session.session_key
            if not session_id:
                request.session.create()
                session_id = request.session.session_key
            game_session.session_id = session_id

        # Note: For in-memory sessions, we don't create welcome messages here
        # since they can't be saved to the database. The welcome message will be
        # created when the session is properly saved to the database.

        return game_session

@ensure_csrf_cookie
def index(request):
    """
    Main view function that renders the game interface
    """
    # Check if user is authenticated
    is_anonymous = not request.user.is_authenticated

    # Get company information if available
    company = None
    company_id = request.session.get('active_company_id')
    if company_id:
        try:
            # Company is already imported at the module level
            company = Company.objects.get(id=company_id)
        except (Company.DoesNotExist, ValueError):
            company = None

    # Get company from URL parameter if provided
    company_slug = request.GET.get('company')
    if company_slug and not company:
        try:
            # Try to find the company by name (since corporate Company might not have slug)
            company = Company.objects.filter(name=company_slug).first()
            if not company:
                # If not found by name, try to find by ID
                try:
                    company_id = int(company_slug)
                    company = Company.objects.get(id=company_id)
                except (ValueError, Company.DoesNotExist):
                    company = None

            # Store company ID in session if found
            if company:
                request.session['active_company_id'] = company.id
        except Exception as e:
            logging.error(f"Error getting company from slug: {str(e)}")
            company = None

    # Get team from URL parameter if provided
    team = request.GET.get('team', '')

    # Get company settings if available
    company_settings = None
    if company:
        try:
            # CompanyGameSettings is already imported at the module level
            company_settings, created = CompanyGameSettings.objects.get_or_create(company=company)
        except Exception as e:
            logging.error(f"Error getting company settings: {str(e)}")

    # Check if game is accessible based on company settings
    game_accessible = True
    if company_settings:
        if not company_settings.is_public and is_anonymous:
            game_accessible = False
        if company_settings.require_login and is_anonymous:
            game_accessible = False

    # Get or create game session for authenticated users
    game_session = None
    if not is_anonymous:
        game_session = get_or_create_game_session(request)

    return render(request, 'game/index.html', {
        'is_anonymous': is_anonymous,
        'company': company,
        'team': team,
        'company_settings': company_settings,
        'game_accessible': game_accessible,
        'game_session': game_session
    })

@csrf_exempt
def log_error(request):
    """
    API endpoint to log client-side errors
    """
    if request.method != 'POST':
        return JsonResponse({"status": "error", "message": "Method not allowed"}, status=405)

    try:
        data = json.loads(request.body)
        message = data.get('message', 'Unknown error')
        source = data.get('source', 'Unknown source')
        lineno = data.get('lineno', 0)
        colno = data.get('colno', 0)
        error_stack = data.get('error', None)

        logging.error(f"Client error: {message} at {source}:{lineno}:{colno}")
        if error_stack:
            logging.error(f"Error stack: {error_stack}")

        return JsonResponse({"status": "success"})
    except Exception as e:
        logging.error(f"Error logging client error: {str(e)}")
        return JsonResponse({"status": "error", "message": str(e)}, status=500)

def preview_page(request):
    """
    View function that renders the preview interface
    """
    return render(request, 'game/preview.html')

def start_game(request):
    """
    API endpoint to start a new game
    """
    try:
        # Check if this is a restart request
        is_restart = 'restart' in request.GET or 't' in request.GET
        logging.info(f"Starting game with restart={is_restart}")

        # Get or create game session
        game_session = get_or_create_game_session(request)

        # Log the game session ID for debugging
        logging.info(f"Game session ID: {getattr(game_session, 'id', None)}")

        # BUGFIX: If this is a restart, delete all existing messages to prevent duplicates
        if is_restart and hasattr(game_session, 'id'):
            logging.info(f"Restart requested - deleting all existing messages for game session {game_session.id}")
            try:
                Message.objects.filter(game_session=game_session).delete()
                logging.info(f"Deleted all messages for game session {game_session.id}")
            except Exception as e:
                logging.error(f"Error deleting messages: {str(e)}")

        # Reset game session to initial state
        game_session.current_role = "applicant"
        game_session.performance_score = 0
        game_session.challenges_completed = 0
        game_session.role_challenges_completed = 0
        game_session.game_completed = False
        game_session.current_manager = "hr"
        game_session.current_task = "cover_letter"
        game_session.set_completed_roles([])
        game_session.first_task_pending = True

        # Make sure next_task_pending is set
        if hasattr(game_session, 'next_task_pending'):
            game_session.next_task_pending = False

        game_session.save()

        # Delete all existing messages for this session
        message_count_before = game_session.messages.count()
        logging.info(f"Deleting {message_count_before} messages for game session {game_session.id}")

        # Get all message IDs for logging
        if hasattr(game_session, 'id'):
            message_ids = list(game_session.messages.values_list('id', flat=True))
            logging.info(f"Message IDs to delete: {message_ids}")

            # Delete messages using Django ORM
            deleted_count = Message.objects.filter(game_session=game_session).delete()[0]
            logging.info(f"Deleted {deleted_count} messages using Django ORM")

            # Verify deletion
            message_count_after = game_session.messages.count()
            logging.info(f"After deletion: {message_count_after} messages remain for game session {game_session.id}")

        # Add welcome message
        company_name = "Rwenzori Innovations Ltd."
        if game_session.company:
            company_name = game_session.company.name

        # Check if there's a custom welcome message in company settings
        custom_welcome = ""
        if game_session.company:
            try:
                settings = CompanyGameSettings.objects.get(company=game_session.company)
                if settings.custom_welcome_message:
                    custom_welcome = f"\n\n{settings.custom_welcome_message}"
            except Exception as e:
                logging.error(f"Error getting company welcome message: {str(e)}")

        welcome_text = f'''# Welcome to {company_name}

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.{custom_welcome}'''
        welcome_html = markdown.markdown(welcome_text, extensions=['extra'])

        # Create welcome message with a specific message_id to make it identifiable
        welcome_message_id = str(uuid.uuid4())
        welcome_message = Message.objects.create(
            game_session=game_session,
            message_id=welcome_message_id,
            sender="hr",
            text=welcome_text,
            html=welcome_html,
            is_challenge=False,
            is_markdown=True,
            # Set timestamp to a slightly earlier time to ensure it appears first
            timestamp=timezone.now() - datetime.timedelta(seconds=10)
        )
        logging.info(f"Created welcome message with ID: {welcome_message_id} and database ID: {welcome_message.id}")

        # Get the first task
        first_task = get_all_role_tasks("applicant")[0]

        # Get challenges required for current role
        current_role = game_session.current_role
        role_info = ROLE_PROGRESSION.get(current_role, {})
        challenges_required = role_info.get("challenges_required", 3)

        # Generate role progression visualization
        try:
            role_progression_html = generate_role_progression_html(
                game_session.current_role,
                game_session.get_completed_roles()
            )
        except Exception as e:
            logging.error(f"Error generating role progression: {str(e)}")
            role_progression_html = "<div>Role progression visualization not available</div>"

        # Generate org chart
        org_chart_html = generate_org_chart_html(
            game_session.current_role,
            game_session.get_completed_roles()
        )

        # Add the first task message with a specific message_id to make it identifiable
        first_task_html = markdown.markdown(first_task["description"], extensions=['extra'])
        first_task_message_id = str(uuid.uuid4())
        first_task_message = Message.objects.create(
            game_session=game_session,
            message_id=first_task_message_id,
            sender=first_task["manager"],
            text=first_task["description"],
            html=first_task_html,
            is_challenge=True,
            task_id=first_task["id"],
            is_markdown=True
        )
        logging.info(f"Added first task message: {first_task['id']} with message_id: {first_task_message_id} and database ID: {first_task_message.id}")

        # Get updated game state
        game_state = game_session.to_dict()

        return JsonResponse({
            "status": "success",
            "messages": game_state["messages"],
            "current_role": game_state["current_role"],
            "performance_score": game_state["performance_score"],
            "characters": CHARACTERS,
            "current_manager": game_state["current_manager"],
            "current_task": game_state["current_task"],
            "challenges_required": challenges_required,
            "role_progression_html": role_progression_html,
            "org_chart_html": org_chart_html,
            "first_task_pending": False,  # Set to False since we're adding the first task
            "challenges_completed": game_state["challenges_completed"],
            "role_challenges_completed": game_state["role_challenges_completed"],
            "completed_roles": game_state["completed_roles"]
        })
    except Exception as e:
        logging.error(f"Error in start_game: {str(e)}")
        import traceback
        logging.error(traceback.format_exc())
        return JsonResponse({
            "status": "error",
            "message": f"An error occurred while starting the game: {str(e)}"
        }, status=500)

@csrf_exempt
def submit_prompt(request):
    """
    API endpoint to submit a prompt
    """
    if request.method != 'POST':
        return JsonResponse({"status": "error", "message": "Method not allowed"}, status=405)

    try:
        # Get game session
        game_session = get_or_create_game_session(request)

        # Parse request data
        data = json.loads(request.body)
        prompt = data.get('prompt', '')
        preview_only = data.get('preview_only', False)
        edited_response = data.get('edited_response')
        current_manager = data.get('current_manager', 'hr')
        current_task_id = data.get('current_task', 'cover_letter')

        # Validate input
        if not prompt:
            return JsonResponse({"status": "error", "message": "Prompt cannot be empty"})

        # Get current task
        current_role = game_session.current_role
        role_tasks = get_all_role_tasks(current_role)

        # Find the current task in the role tasks
        current_task = None
        for task in role_tasks:
            if task["id"] == current_task_id:
                current_task = task
                break

        # If task not found, use the first task for the role
        if not current_task and role_tasks:
            current_task = role_tasks[0]
            game_session.current_task = current_task["id"]
            game_session.current_manager = current_task["manager"]
            game_session.save()

        # If preview only, generate preview and return
        if preview_only:
            return preview_response(request)

        # Check if this exact user message already exists to avoid duplicates
        existing_user_message = Message.objects.filter(
            game_session=game_session,
            sender="user",
            text=prompt
        ).first()

        if existing_user_message:
            # Use the existing message
            user_message = existing_user_message
            logging.info(f"Using existing user message: {user_message.id}")
        else:
            # Add user message with sender="user" to ensure it shows as sent by the user
            user_html = markdown.markdown(prompt, extensions=['extra'])
            user_message = Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender="user",  # This will be displayed as "You" in the frontend
                text=prompt,
                html=user_html,
                is_challenge=False,
                is_markdown=True
            )
            logging.info(f"Created new user message: {user_message.id}")

        # Generate AI response
        if LLM_RESPONSE_AVAILABLE:
            # Use LLM to generate response
            ai_response = generate_response_with_llm(
                prompt=prompt,
                task_id=current_task["id"] if current_task else None
            )
            if current_task and ai_response == current_task.get("response_template"):
                logging.warning("LLM response matches template response exactly. This might indicate the LLM response is not being used.")
                ai_response = "This is a forced response to ensure we're not using the template. " + ai_response
        else:
            ai_response = current_task.get("response_template", "This is a template response.")

        html_response = markdown.markdown(ai_response, extensions=['extra'])

        # Check if this exact AI response already exists to avoid duplicates
        existing_ai_message = Message.objects.filter(
            game_session=game_session,
            sender="ai",
            text=ai_response
        ).first()

        if existing_ai_message:
            # Use the existing message
            ai_message = existing_ai_message
            logging.info(f"Using existing AI message: {ai_message.id}")
        else:
            # Create a new AI response message
            ai_message = Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender="ai",
                text=ai_response,
                html=html_response,
                is_challenge=False,
                is_markdown=True
            )
            logging.info(f"Created new AI message: {ai_message.id}")

        prompt_eval_data = None
        grade = "bad"
        similarity_score = 0
        raw_feedback_details = ["Error: Evaluation could not be performed."]
        evaluation_results_for_processing = {"overall_score": 0, "meets_requirements": False}

        llm_failure_message_start = "I apologize, but I'm currently unable to generate a detailed response due to technical difficulties."
        if ai_response.startswith(llm_failure_message_start):
            logging.warning("AI response is a fallback message. Overriding evaluation to reflect system failure.")
            grade = "bad"
            similarity_score = 5
            raw_feedback_details = ["System error: The AI was unable to generate a response to your prompt. Please try again later."]
            evaluation_results_for_processing = {"overall_score": 0.5, "meets_requirements": False}
            prompt_eval_data = {"status": "error", "message": "AI response generation failed."}
        elif PROMPT_EVALUATION_AVAILABLE:
            try:
                logging.info(f"Attempting LLM-based prompt evaluation for task {current_task_id}")
                prompt_eval_data = evaluate_prompt(prompt, current_task_id)
                logging.info(f"Prompt evaluation result: {prompt_eval_data}")

                if prompt_eval_data and prompt_eval_data.get("status") == "error":
                    logging.warning(f"Prompt evaluation returned an error: {prompt_eval_data.get('message')}")
                    return JsonResponse({
                        "status": "error",
                        "message": prompt_eval_data.get('message', "Evaluation service unavailable. Please try again."),
                        "error_type": prompt_eval_data.get('error_type', "unknown"),
                        "is_offline": prompt_eval_data.get('error_type') == "offline"
                    })

                if prompt_eval_data and prompt_eval_data.get("status") == "success":
                    grade = prompt_eval_data.get("grade", "bad")
                    similarity_score = prompt_eval_data.get("overall_score", 0)
                    raw_feedback_details = prompt_eval_data.get("feedback_details", ["Evaluation successful but no details provided."])
                    evaluation_results_for_processing = {
                        "overall_score": similarity_score / 10 if similarity_score > 10 else similarity_score,
                        "meets_requirements": prompt_eval_data.get("meets_requirements", False)
                    }
                    logging.info(f"Processed prompt_eval_data: grade={grade}, similarity_score={similarity_score}, meets_requirements={evaluation_results_for_processing['meets_requirements']}")
                else:
                    logging.error(f"Prompt evaluation did not return success. Data: {prompt_eval_data}")
                    evaluation_results_for_processing = {"overall_score": 0, "meets_requirements": False}
            except Exception as e:
                logging.error(f"LLM-based prompt evaluation failed critically: {str(e)}")
                evaluation_results_for_processing = {"overall_score": 0, "meets_requirements": False}
        else:
            logging.warning("PROMPT_EVALUATION_AVAILABLE is False. Returning error message.")
            return JsonResponse({
                "status": "error",
                "message": "Evaluation service unavailable. Please check your internet connection and try again.",
                "error_type": "offline",
                "is_offline": True
            })

        final_feedback_grade = "good" if similarity_score >= 90 else grade
        performance_score_for_feedback_0_10 = similarity_score / 10 if similarity_score > 10 else similarity_score
        feedback_text = ""

        if LLM_FEEDBACK_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_FEEDBACK", "true").lower() == "true":
            try:
                metrics_for_llm_feedback = {
                    "overall_score": performance_score_for_feedback_0_10,
                    "grade": final_feedback_grade,
                    "feedback_details": raw_feedback_details,
                    "formatted_score": f"{performance_score_for_feedback_0_10:.1f}"
                }
                manager_character = CHARACTERS.get(current_task["manager"], {})
                manager_name_for_llm_feedback = manager_character.get("name", current_task["manager"])
                feedback_text = llm_generate_manager_feedback(
                    prompt, current_task["id"], manager_name_for_llm_feedback, metrics_for_llm_feedback
                )
                if f"{performance_score_for_feedback_0_10 * 10:.0f}/100" not in feedback_text and f"{performance_score_for_feedback_0_10:.1f}/10" not in feedback_text :
                    feedback_text += f"\n\n(Overall Score: {performance_score_for_feedback_0_10 * 10:.0f}/100)"
            except Exception as fb_err:
                logging.error(f"Error generating LLM feedback: {fb_err}")
                feedback_text = current_task["feedback"].get(final_feedback_grade, "Could not generate personalized feedback.")
                feedback_text += f"\n\n(Overall Score: {performance_score_for_feedback_0_10 * 10:.0f}/100)"
        else:
            feedback_text = current_task["feedback"].get(final_feedback_grade, "Feedback based on your performance.")
            feedback_text += f"\n\n(Overall Score: {performance_score_for_feedback_0_10 * 10:.0f}/100)"

        feedback_html = markdown.markdown(feedback_text, extensions=['extra'])

        # Check if this exact feedback message already exists to avoid duplicates
        existing_feedback_message = Message.objects.filter(
            game_session=game_session,
            sender=current_task["manager"],
            text=feedback_text
        ).first()

        if existing_feedback_message:
            # Use the existing message
            feedback_message = existing_feedback_message
            logging.info(f"Using existing feedback message: {feedback_message.id}")
        else:
            # Create a new feedback message
            feedback_message = Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender=current_task["manager"],
                text=feedback_text,
                html=feedback_html,
                timestamp=timezone.now(),
                is_challenge=False,
                is_markdown=True
            )
            logging.info(f"Created new feedback message: {feedback_message.id}")

        points_earned = 10 if grade == "good" else (5 if grade == "okay" else 0)
        logging.info(f"Points earned from evaluation: {points_earned} (grade: {grade})")
        game_session.performance_score = (game_session.performance_score or 0) + points_earned

        promoted = False
        next_task_pending_flag = False
        just_completed_task_flag = False
        role_before_processing = game_session.current_role
        task_passed_condition = grade == "good"

        if task_passed_condition:
            logging.info("Task grade is good. Processing completion.")
            game_session.challenges_completed = (game_session.challenges_completed or 0) + 1
            game_session.role_challenges_completed = (game_session.role_challenges_completed or 0) + 1
            temp_game_state_dict = game_session.to_dict()
            temp_game_state_dict["messages"] = [msg.to_dict() for msg in game_session.messages.all().order_by('timestamp')]

            updated_game_state_from_manager = process_task_completion(
                temp_game_state_dict,
                evaluation_results_for_processing,
                points_earned
            )

            game_session.current_role = updated_game_state_from_manager["current_role"]
            game_session.performance_score = updated_game_state_from_manager["performance_score"]
            game_session.challenges_completed = updated_game_state_from_manager["challenges_completed"]
            game_session.role_challenges_completed = updated_game_state_from_manager["role_challenges_completed"]
            game_session.game_completed = updated_game_state_from_manager["game_completed"]
            game_session.current_manager = updated_game_state_from_manager["current_manager"]
            game_session.current_task = updated_game_state_from_manager["current_task"]
            game_session.set_completed_roles(updated_game_state_from_manager["completed_roles"])

            # Copy next_task_pending flag if it exists
            if "next_task_pending" in updated_game_state_from_manager:
                game_session.next_task_pending = updated_game_state_from_manager["next_task_pending"]
                logging.info(f"Set next_task_pending to {game_session.next_task_pending}")

            logging.info(f"Task completion processed: role={game_session.current_role}, task={game_session.current_task}, challenges_completed={game_session.role_challenges_completed}")

            if game_session.current_role != role_before_processing:
                promoted = True
                logging.info(f"Player promoted from {role_before_processing} to {game_session.current_role}")

                if "promotion_message" not in updated_game_state_from_manager:
                    from .role_progression import get_promotion_message
                    promotion_message_text = get_promotion_message(role_before_processing)

                    # Get the next manager information for the new role
                    next_manager_for_new_role_id = game_session.current_manager
                    next_manager_for_new_role_info = CHARACTERS.get(next_manager_for_new_role_id, {})
                    next_manager_for_new_role_name = next_manager_for_new_role_info.get("name", next_manager_for_new_role_id)
                    next_manager_for_new_role_title = next_manager_for_new_role_info.get("title", "")

                    # Create the promotion message with manager information
                    full_promotion_text = f"{promotion_message_text}\n\nYou will be reporting to **{next_manager_for_new_role_name}** ({next_manager_for_new_role_title}) for your next tasks."
                    promotion_html = markdown.markdown(full_promotion_text, extensions=['extra'])

                    # We no longer delete old promotion messages
                    # This ensures all promotion history is preserved
                    logging.info("Keeping all previous promotion messages for history")

                    # Create the new promotion message
                    message_data = {
                        'game_session': game_session, 'message_id': str(uuid.uuid4()), 'sender': "ceo",
                        'text': full_promotion_text, 'html': promotion_html, 'timestamp': timezone.now(),
                        'is_challenge': False, 'is_markdown': True
                    }

                    try:
                        from django.db import models
                        if hasattr(Message, 'is_promotion'):
                            Message.objects.create(**message_data, is_promotion=True)
                        else:
                            Message.add_to_class('is_promotion', models.BooleanField(default=False))
                            Message.objects.create(**message_data, is_promotion=True)
                        logging.info(f"Created self-contained promotion message with next manager info.")
                    except Exception as e:
                        logging.warning(f"Could not create promotion message with is_promotion field: {str(e)}")
                        Message.objects.create(**message_data)
                    logging.info(f"Added promotion message: {full_promotion_text}")


                if not game_session.game_completed:
                    new_role_first_task_id = game_session.current_task
                    new_role_tasks = get_all_role_tasks(game_session.current_role)
                    actual_new_task_details = next((t for t in new_role_tasks if t["id"] == new_role_first_task_id), None)

                    if actual_new_task_details:
                        logging.info(f"Promotion: Preparing first task of new role '{game_session.current_role}': {actual_new_task_details['id']}")

                        # BUGFIX: Check if this task message already exists before creating a new one
                        existing_task_message = Message.objects.filter(
                            game_session=game_session,
                            task_id=actual_new_task_details["id"],
                            is_challenge=True
                        ).order_by('-timestamp').first()

                        if existing_task_message:
                            logging.info(f"Using existing task message for first task after promotion: {actual_new_task_details['id']}")
                            # No need to create a new message
                        else:
                            # Create a new task message
                            next_task_html = markdown.markdown(actual_new_task_details["description"], extensions=['extra'])
                            Message.objects.create(
                                game_session=game_session, message_id=str(uuid.uuid4()), sender=actual_new_task_details["manager"],
                                text=actual_new_task_details["description"], html=next_task_html, timestamp=timezone.now(),
                                is_challenge=True, task_id=actual_new_task_details["id"], is_markdown=True
                            )
                            logging.info(f"Created new task message for first task after promotion: {actual_new_task_details['id']}")

                        next_task_pending_flag = False
                        if hasattr(game_session, 'next_task_pending'):
                             game_session.next_task_pending = False
                    else:
                        logging.error(f"Could not find task details for new role's first task ID: {new_role_first_task_id}")
                        next_task_pending_flag = False
                        if hasattr(game_session, 'next_task_pending'):
                            game_session.next_task_pending = False

                if updated_game_state_from_manager.get("current_task"):
                    game_session.current_task = updated_game_state_from_manager["current_task"]
                if updated_game_state_from_manager.get("current_manager"):
                    game_session.current_manager = updated_game_state_from_manager["current_manager"]

            if not promoted and not game_session.game_completed:
                just_completed_task_flag = True
                tasks_for_current_role = get_all_role_tasks(game_session.current_role)
                current_task_index_in_role = next((i for i, t_obj in enumerate(tasks_for_current_role) if t_obj["id"] == current_task_id), -1)

                next_task_index_in_role = current_task_index_in_role + 1
                if next_task_index_in_role < len(tasks_for_current_role):
                    next_task_details = tasks_for_current_role[next_task_index_in_role]
                    game_session.current_task = next_task_details["id"]
                    game_session.current_manager = next_task_details["manager"]

                    # BUGFIX: Check if this task message already exists before creating a new one
                    existing_task_message = Message.objects.filter(
                        game_session=game_session,
                        task_id=next_task_details["id"],
                        is_challenge=True
                    ).order_by('-timestamp').first()

                    if existing_task_message:
                        logging.info(f"Using existing task message for next task: {next_task_details['id']}")
                        # No need to create a new message
                    else:
                        # Create a new task message
                        next_task_html = markdown.markdown(next_task_details["description"], extensions=['extra'])
                        Message.objects.create(
                            game_session=game_session, message_id=str(uuid.uuid4()), sender=next_task_details["manager"],
                            text=next_task_details["description"], html=next_task_html, timestamp=timezone.now(),
                            is_challenge=True, task_id=next_task_details["id"], is_markdown=True
                        )
                        logging.info(f"Created new task message for next task: {next_task_details['id']}")

                    logging.info(f"Next task in current role set: {next_task_details['id']}")
                    next_task_pending_flag = False
                else:
                    if ROLE_PROGRESSION.get(game_session.current_role, {}).get("next_role") is None:
                        game_session.game_completed = True
                    elif current_task:
                        logging.warning(f"Ran out of unique tasks for role {game_session.current_role} before promotion. Re-issuing task: {current_task['id']}")
                        if not Message.objects.filter(game_session=game_session, is_challenge=True, task_id=current_task["id"]).exists():
                            reissue_task_html = markdown.markdown(current_task["description"], extensions=['extra'])
                            Message.objects.create(
                                game_session=game_session, message_id=str(uuid.uuid4()), sender=current_task["manager"],
                                text=current_task["description"], html=reissue_task_html, timestamp=timezone.now(),
                                is_challenge=True, task_id=current_task["id"], is_markdown=True
                            )
                        next_task_pending_flag = False
                    else:
                        logging.error(f"Logic error: current_task object not available for re-issue.")
                        next_task_pending_flag = False

            if game_session.game_completed:
                just_completed_task_flag = True
                next_task_pending_flag = False
                # Get company name
                company_name = "Rwenzori Innovations"
                if game_session.company:
                    company_name = game_session.company.name

                # Check if there's a custom completion message in company settings
                custom_completion = ""
                if game_session.company:
                    try:
                        from .models import CompanyGameSettings
                        settings = CompanyGameSettings.objects.get(company=game_session.company)
                        if settings.custom_completion_message:
                            custom_completion = f"\n\n{settings.custom_completion_message}"
                    except Exception as e:
                        logging.error(f"Error getting company completion message: {str(e)}")

                completion_text_parts = [
                    "## Congratulations! 🎉",
                    f"You've completed all the challenges and proven your prompt engineering skills! You've successfully progressed through all departments of {company_name}:",
                    "### Entry Level",
                    "- Started as an **Applicant** in HR",
                    "- Became a **Junior Assistant** in HR",
                    "### Marketing Department",
                    "- Advanced to **Sales Associate** in Marketing",
                    "- Promoted to **Marketing Associate** in Marketing",
                    "- Became a **Sales Manager** in Marketing",
                    "- Moved up to **Advertising/Research Manager** in Marketing",
                    "- Reached **Vice-President of Marketing**",
                    "### Operations Department",
                    "- Transferred to **Service Associate** in Operations",
                    "- Moved to **Production Associate** in Operations",
                    "- Advanced to **Facilities Associate** in Operations",
                    "- Promoted to **Service Manager** in Operations",
                    "- Became a **Production Manager** in Operations",
                    "- Moved up to **Facilities Manager** in Operations",
                    "- Reached **Vice-President of Operations**",
                    "### Finance Department",
                    "- Transferred to **Accounts Receivable Associate** in Finance",
                    "- Moved to **Accounts Payable Associate** in Finance",
                    "- Promoted to **Accounts Receivable Manager** in Finance",
                    "- Advanced to **Accounts Payable Manager** in Finance",
                    "- Reached **Vice-President of Finance**",
                    "### HR Department",
                    "- Transferred to **HR Coordinator** in Human Resources",
                    "- Promoted to **HR Manager** in Human Resources",
                    "- Advanced to **HR Director** in Human Resources",
                    "### Executive Level",
                    "- Promoted to **Chief Operating Officer (COO)**",
                    "- Advanced to **Chief Executive Officer (CEO)**",
                    "- And finally joined the **Shareholders** at the very top of Rwenzori Innovations!",
                    "Thank you for demonstrating your exceptional abilities in crafting effective prompts and managing AI interactions across all levels of the organization."
                ]

                # Generate certificate if user is authenticated
                certificate = None
                if game_session.user and game_session.user.is_authenticated:
                    try:
                        # Generate certificate
                        certificate = game_session.generate_certificate()

                        # Update leaderboard
                        game_session.update_leaderboard()

                        # Add certificate information to completion message
                        if certificate:
                            completion_text_parts.append("\n\n### Your Certificate")
                            completion_text_parts.append(f"A certificate of completion has been generated for you. You can view and download it from your corporate dashboard.")
                            completion_text_parts.append(f"Certificate Verification Code: **{certificate.verification_code}**")
                    except Exception as cert_err:
                        logging.error(f"Error generating certificate: {str(cert_err)}")

                # Add custom completion message if available
                if custom_completion:
                    completion_text_parts.append(custom_completion)

                completion_text = "\n\n".join(completion_text_parts)
                completion_html = markdown.markdown(completion_text, extensions=['extra'])
                try:
                    from django.db import models
                    if hasattr(Message, 'is_promotion'):
                        Message.objects.create(
                            game_session=game_session, message_id=str(uuid.uuid4()), sender="ceo",
                            text=completion_text, html=completion_html, timestamp=timezone.now(),
                            is_challenge=False, is_markdown=True, is_promotion=True
                        )
                    else:
                         Message.objects.create(
                            game_session=game_session, message_id=str(uuid.uuid4()), sender="ceo",
                            text=completion_text, html=completion_html, timestamp=timezone.now(),
                            is_challenge=False, is_markdown=True
                        )
                    logging.info("Game completed message added.")
                except Exception as e:
                    logging.warning(f"Error creating game completion message: {str(e)}")
        else:
            logging.info("Task failed (grade was not 'good'). Player stays on the current task.")
            promoted = False
            just_completed_task_flag = False
            next_task_pending_flag = False

        game_session.save()

        # Update the leaderboard if the user is authenticated
        if game_session.user and game_session.user.is_authenticated:
            try:
                # Update leaderboard with current game session results
                game_session.update_leaderboard()
                logging.info(f"Updated leaderboard for user {game_session.user.username} with score {game_session.performance_score}")
            except Exception as e:
                logging.error(f"Error updating leaderboard: {str(e)}")
                import traceback
                logging.error(traceback.format_exc())

        final_role_info = ROLE_PROGRESSION.get(game_session.current_role, {})
        final_challenges_required = final_role_info.get("challenges_required", 3)
        final_role_progression_html = generate_role_progression_html(game_session.current_role, game_session.get_completed_roles())
        final_org_chart_html = generate_org_chart_html(game_session.current_role, game_session.get_completed_roles())

        response_data = {
            "status": "success",
            "messages": [msg.to_dict() for msg in game_session.messages.all().order_by('timestamp')],
            "current_role": game_session.current_role,
            "performance_score": game_session.performance_score,
            "promoted": promoted,
            "is_promotion": promoted,
            "just_completed_task": just_completed_task_flag,
            "game_completed": game_session.game_completed,
            "grade": grade,
            "current_manager": game_session.current_manager,
            "manager_name": CHARACTERS.get(game_session.current_manager, {}).get("name", "Unknown"),
            "current_task": game_session.current_task,
            "role_challenges_completed": game_session.role_challenges_completed,
            "total_challenges_completed": game_session.challenges_completed,
            "completed_roles": game_session.get_completed_roles(),
            "next_task_pending": next_task_pending_flag,
            "challenges_required": final_challenges_required,
            "similarity_score": similarity_score,
            "overall_score": performance_score_for_feedback_0_10,
            "feedback_details": raw_feedback_details,
            "role_progression_html": final_role_progression_html,
            "org_chart_html": final_org_chart_html,
            "meets_requirements": task_passed_condition,
            "debug_info": {
                "promoted": promoted,
                "just_completed_task": just_completed_task_flag,
                "next_task_pending": next_task_pending_flag,
                "current_task": game_session.current_task,
                "current_manager": game_session.current_manager,
                "role_challenges_completed": game_session.role_challenges_completed
            }
        }
        if prompt_eval_data and prompt_eval_data.get("status") == "success":
            response_data["prompt_dimensions"] = prompt_eval_data.get("dimensions", {})
            response_data["prompt_evaluation_summary"] = prompt_eval_data.get("summary", "")
            response_data["prompt_improvement_suggestions"] = prompt_eval_data.get("improvement_suggestions", [])

        return JsonResponse(response_data)

    except Exception as e:
        logging.error(f"Error in submit_prompt: {str(e)}")
        return JsonResponse({"status": "error", "message": str(e)})

def get_game_state(request):
    """
    API endpoint to get the current game state
    """
    # Get game session
    game_session = get_or_create_game_session(request)

    # Get challenges required for current role
    current_role = game_session.current_role
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)

    # Generate role progression visualization
    try:
        from .role_tasks import generate_role_progression_html
        role_progression_html = generate_role_progression_html(
            game_session.current_role,
            game_session.get_completed_roles()
        )
    except ImportError:
        role_progression_html = "<div>Role progression visualization not available</div>"

    # Generate org chart
    org_chart_html = generate_org_chart_html(
        game_session.current_role,
        game_session.get_completed_roles()
    )

    # Convert game session to dictionary - this includes all messages
    game_state = game_session.to_dict()

    # Prepare response
    response_data = {
        "status": "success",
        "messages": game_state["messages"],
        "current_role": game_state["current_role"],
        "performance_score": game_state["performance_score"],
        "game_completed": game_state["game_completed"],
        "current_manager": game_state["current_manager"],
        "current_task": game_session.current_task,
        "role_challenges_completed": game_state.get("role_challenges_completed", 0),
        "total_challenges_completed": game_state.get("challenges_completed", 0),
        "completed_roles": game_state.get("completed_roles", []),
        "challenges_required": challenges_required,
        "role_progression_html": role_progression_html,
        "org_chart_html": org_chart_html,
        "characters": CHARACTERS,
        "first_task_pending": game_state.get("first_task_pending", False),
        "next_task_pending": game_state.get("next_task_pending", False)
    }

    return JsonResponse(response_data)

def preview_response(request):
    """
    API endpoint to preview a response
    """
    # Get prompt and task ID from request
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            prompt = data.get('prompt', '')
            task_id = data.get('task_id', 'cover_letter')
        except:
            prompt = ''
            task_id = 'cover_letter'
    else:
        prompt = request.GET.get('prompt', '')
        task_id = request.GET.get('task_id', 'cover_letter')

    if not prompt:
        return JsonResponse({"status": "error", "message": "Prompt cannot be empty"})

    try:
        # Generate preview response
        if LLM_RESPONSE_AVAILABLE:
            # Use LLM to generate response
            ai_response = generate_response_with_llm(
                prompt=prompt,
                task_id=task_id
            )
        else:
            # Use template response
            current_role = "applicant"  # Default role
            role_tasks = get_all_role_tasks(current_role)
            current_task = next((task for task in role_tasks if task["id"] == task_id), None)

            if current_task and "response_template" in current_task:
                if task_id == "cover_letter":
                    ai_response = """Dear Hiring Manager,

I am writing to express my enthusiastic interest in the Junior Assistant position at Rwenzori Innovations Ltd. as advertised. Having followed Rwenzori Innovations Ltd.'s innovative contributions to the solar solutions industry for some time, I am impressed by your commitment to quality and exceptional customer service, and I am eager to contribute to your team's success.

My background and skills align well with the requirements of this role. I possess excellent communication skills, both written and verbal, honed through academic experiences and previous roles. My strong attention to detail and organizational abilities have proven valuable in managing multiple tasks and prioritizing effectively. My proficiency in the Microsoft Office Suite, including Word, Excel, and PowerPoint, enables me to efficiently prepare documents, presentations, and manage data.

In my previous role, I was responsible for coordinating meetings, managing calendars, and maintaining organized filing systems. I consistently demonstrated a proactive attitude, identifying ways to support various departments and contribute to team projects and initiatives. I am confident in my ability to quickly adapt to new environments and contribute meaningfully to your operations.

Thank you for considering my application. I am excited about the possibility of joining your team and contributing to Rwenzori Innovations Ltd.'s continued success. I have attached my resume for your review and welcome the opportunity to discuss my qualifications further in an interview.

Sincerely,
[Your Name]"""
                else:
                    ai_response = current_task["response_template"]
            else:
                ai_response = f"This is a preview response for: {prompt}"

        html_response = markdown.markdown(ai_response, extensions=['extra'])

        if PROMPT_EVALUATION_AVAILABLE:
            evaluation_results = evaluate_prompt(prompt, task_id)
            if evaluation_results.get("status") == "error":
                logging.warning(f"Prompt evaluation returned an error: {evaluation_results.get('message')}")
                return JsonResponse({
                    "status": "error",
                    "message": evaluation_results.get('message', "Evaluation service unavailable. Please try again."),
                    "error_type": evaluation_results.get('error_type', "unknown"),
                    "is_offline": evaluation_results.get('error_type') == "offline"
                })

            grade = evaluation_results.get("grade", "okay")
            overall_score = evaluation_results.get("overall_score", 50)
            feedback_details = evaluation_results.get("feedback_details", [])
            dimensions = evaluation_results.get("dimensions", {})
            improvement_suggestions = evaluation_results.get("improvement_suggestions", [])
            summary = evaluation_results.get("summary", "")
            similarity_score = overall_score
            prompt_dimensions = dimensions
        else:
            logging.warning("PROMPT_EVALUATION_AVAILABLE is False. Returning error message for preview.")
            return JsonResponse({
                "status": "error",
                "message": "Evaluation service unavailable. Please check your internet connection and try again.",
                "error_type": "offline",
                "is_offline": True
            })

        preview_feedback_text = ""
        if LLM_FEEDBACK_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_FEEDBACK", "true").lower() == "true":
            try:
                preview_perf_score_0_10 = similarity_score / 10 if similarity_score > 10 else similarity_score
                all_tasks_for_preview_role = get_all_role_tasks("applicant")
                preview_task_details = next((t for t in all_tasks_for_preview_role if t["id"] == task_id), None)
                manager_id_for_preview = preview_task_details["manager"] if preview_task_details else "hr"
                manager_character_for_preview = CHARACTERS.get(manager_id_for_preview, {})
                manager_name_for_preview = manager_character_for_preview.get("name", manager_id_for_preview)

                preview_performance_metrics = {
                    "overall_score": preview_perf_score_0_10,
                    "display_score": similarity_score,
                    "formatted_score": f"{similarity_score:.0f}%",
                    "grade": grade,
                    "feedback_details": feedback_details
                }
                preview_feedback_text = llm_generate_manager_feedback(
                    prompt, task_id, manager_name_for_preview, preview_performance_metrics
                )
            except Exception as fb_err:
                logging.error(f"Error generating LLM feedback for preview: {fb_err}")
                preview_feedback_text = ""

        response_data = {
            "status": "success",
            "ai_response": ai_response,
            "html_response": html_response,
            "ai_response_html": html_response,
            "prompt_evaluation_grade": grade,
            "similarity_score": similarity_score,
            "feedback_details": feedback_details,
            "meets_requirements": evaluation_results.get("meets_requirements", True) if PROMPT_EVALUATION_AVAILABLE and evaluation_results else True,
            "overall_score": overall_score,
            "prompt_dimensions": prompt_dimensions,
            "prompt_improvement_suggestions": improvement_suggestions,
            "prompt_evaluation_summary": summary,
            "preview_feedback": preview_feedback_text,
            "is_offline": not PROMPT_EVALUATION_AVAILABLE
        }
        return JsonResponse(response_data)
    except Exception as e:
        logging.error(f"Error generating preview response: {str(e)}")
        return JsonResponse({"status": "error", "message": f"Error generating preview: {str(e)}"})

def fetch_first_task(request):
    """
    API endpoint to fetch the first task after starting the game
    """
    game_session = get_or_create_game_session(request)
    if not game_session.first_task_pending:
        return JsonResponse({"status": "error", "message": "No pending first task"})

    # Get the first task for the current role
    first_task = get_all_role_tasks(game_session.current_role)[0]

    # Format the task description as HTML
    first_task_html = markdown.markdown(first_task["description"], extensions=['extra'])

    # Log the task information for debugging
    logging.info(f"Fetching first task: {first_task['id']} for role: {game_session.current_role}")
    logging.info(f"Task manager: {CHARACTERS.get(first_task['manager'], {}).get('name', first_task['manager'])} ({first_task['manager']})")

    # BUGFIX: Check if ANY task message already exists for this session
    # This prevents duplicate first tasks from being created
    any_task_message_exists = Message.objects.filter(
        game_session=game_session, is_challenge=True
    ).exists()

    if any_task_message_exists:
        logging.info(f"Task messages already exist for this session. Not creating a duplicate first task.")

        # Check if this specific task message already exists
        existing_message = Message.objects.filter(
            game_session=game_session, task_id=first_task["id"], is_challenge=True
        ).order_by('-timestamp').first()

        if existing_message:
            # Use the existing message
            first_challenge = existing_message
            logging.info(f"Using existing first task message: {first_challenge.id}")
        else:
            # This is unusual - there are task messages but not for this task
            # Just return the first task message that exists
            first_challenge = Message.objects.filter(
                game_session=game_session, is_challenge=True
            ).order_by('timestamp').first()
            logging.info(f"Using alternative task message: {first_challenge.id}")
    else:
        # No task messages exist, create a new one
        first_challenge = Message.objects.create(
            game_session=game_session,
            message_id=str(uuid.uuid4()),
            sender=first_task["manager"],
            text=first_task["description"],
            html=first_task_html,
            is_challenge=True,
            is_markdown=True,
            task_id=first_task["id"]
        )
        logging.info(f"Created new first task message: {first_challenge.id}")

    # Update game session state
    game_session.first_task_pending = False
    game_session.current_task = first_task["id"]
    game_session.current_manager = first_task["manager"]
    game_session.save()

    # Return the task message to the client
    return JsonResponse({"status": "success", "message": first_challenge.to_dict()})

def fetch_next_task(request):
    """
    API endpoint to fetch the next task after a promotion or task completion
    """
    game_session = get_or_create_game_session(request)
    logging.info(f"Fetching next task for role: {game_session.current_role}, current task: {game_session.current_task}")
    role_tasks = get_all_role_tasks(game_session.current_role)

    if not role_tasks:
        logging.error(f"No tasks found for role: {game_session.current_role}")
        return JsonResponse({"status": "error", "message": "No tasks found for current role"})

    next_task = None
    next_task_pending_value = getattr(game_session, 'next_task_pending', False)
    is_first_task_after_promotion = game_session.role_challenges_completed == 0 and len(game_session.get_completed_roles()) > 0

    # BUGFIX: Improved task selection logic
    if next_task_pending_value or is_first_task_after_promotion:
        # After promotion or when next_task_pending is true, we want the first task of the role
        next_task = role_tasks[0]
        logging.info(f"Selected first task of role: {next_task['id']} (after promotion or next_task_pending)")

        # Reset the next_task_pending flag
        if hasattr(game_session, 'next_task_pending'):
            game_session.next_task_pending = False
    else:
        # For normal progression, use the current task from the game session
        task_id_to_fetch = game_session.current_task

        # Find the current task in the role tasks
        current_task_index = next((i for i, t in enumerate(role_tasks) if t["id"] == task_id_to_fetch), -1)

        if current_task_index >= 0:
            # We found the current task, now get the next task in sequence
            next_task_index = current_task_index + 1

            # If there's a next task in this role, use it
            if next_task_index < len(role_tasks):
                next_task = role_tasks[next_task_index]
                logging.info(f"Selected next task in sequence: {next_task['id']} (index {next_task_index})")
            else:
                # If we're at the end of tasks for this role, use the current task
                # This prevents issues when we're waiting for promotion
                next_task = role_tasks[current_task_index]
                logging.info(f"At end of role tasks, keeping current task: {next_task['id']}")
        else:
            # If we can't find the current task, default to the first task
            logging.warning(f"Could not find task ID '{task_id_to_fetch}'. Defaulting to first task.")
            next_task = role_tasks[0]

    # Update game session with new task information
    game_session.current_task = next_task["id"]
    game_session.current_manager = next_task["manager"]

    # Ensure next_task_pending flag is reset
    if hasattr(game_session, 'next_task_pending'):
        game_session.next_task_pending = False

    # Reset role_challenges_completed after promotion
    if is_first_task_after_promotion and hasattr(game_session, 'role_challenges_completed'):
        logging.info("Resetting role_challenges_completed to 0 after promotion")
        game_session.role_challenges_completed = 0

    # Save changes to the database
    game_session.save()

    # Get the manager ID and format the task description as HTML
    manager_id = next_task["manager"]
    next_task_html = markdown.markdown(next_task["description"], extensions=['extra'])

    # Log the manager information for debugging
    manager_info = CHARACTERS.get(manager_id, {})
    logging.info(f"Task manager: {manager_info.get('name', manager_id)} ({manager_id})")

    # BUGFIX: Improved message handling to prevent duplicates while preserving history
    # Check if this task message already exists
    existing_message = Message.objects.filter(
        game_session=game_session, task_id=next_task["id"], is_challenge=True
    ).order_by('-timestamp').first()

    if existing_message:
        # Use the existing message
        next_challenge = existing_message
        logging.info(f"Using existing task message: {next_challenge.id} for task {next_task['id']}")

        # Only update the HTML if necessary (e.g., after promotion)
        if is_first_task_after_promotion:
            existing_message.html = next_task_html
            existing_message.save()
            logging.info(f"Updated HTML for existing task message after promotion")
    else:
        # Create a new task message
        next_challenge = Message.objects.create(
            game_session=game_session,
            message_id=str(uuid.uuid4()),
            sender=next_task["manager"],
            text=next_task["description"],
            html=next_task_html,
            is_challenge=True,
            is_markdown=True,
            task_id=next_task["id"]
        )
        logging.info(f"Created new task message: {next_challenge.id} for task {next_task['id']}")

    # BUGFIX: Don't delete duplicate task messages - this preserves conversation history
    # Instead, the frontend will handle showing only the most recent version of each task

    # Return the task message to the client
    return JsonResponse({
        "status": "success",
        "message": next_challenge.to_dict()
    })
