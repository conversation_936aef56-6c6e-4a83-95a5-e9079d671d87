#!/usr/bin/env python3
"""
Quick test to verify the OpenAI client creation fixes.
"""

import os
import sys
import logging

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_client_creation():
    """Test the new client creation functions."""
    try:
        # Test response generator client creation
        from game.llm_response_generator_openai_format import _create_openai_client
        
        api_key = "test-api-key"
        
        try:
            client = _create_openai_client(api_key)
            logging.info("✓ Client creation function works - no proxies error!")
            return True
        except Exception as e:
            if "proxies" in str(e).lower():
                logging.error(f"✗ Still getting proxies error: {e}")
                return False
            else:
                logging.info(f"✓ Client creation handled error gracefully: {e}")
                return True
                
    except Exception as e:
        logging.error(f"✗ Error importing or testing: {e}")
        return False

if __name__ == "__main__":
    logging.info("Testing OpenAI client creation fix...")
    if test_client_creation():
        logging.info("🎉 Test passed! The fix appears to be working.")
    else:
        logging.error("❌ Test failed. The fix needs more work.")
