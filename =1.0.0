Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: openai in c:\users\<USER>\appdata\roaming\python\python313\site-packages (1.51.2)
Collecting openai
  Downloading openai-1.93.0-py3-none-any.whl.metadata (29 kB)
Requirement already satisfied: anyio<5,>=3.5.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (4.9.0)
Requirement already satisfied: distro<2,>=1.7.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (1.9.0)
Requirement already satisfied: httpx<1,>=0.23.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (0.28.1)
Requirement already satisfied: jiter<1,>=0.4.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (0.9.0)
Requirement already satisfied: pydantic<3,>=1.9.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (2.11.5)
Requirement already satisfied: sniffio in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (1.3.1)
Requirement already satisfied: tqdm>4 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (4.67.1)
Requirement already satisfied: typing-extensions<5,>=4.11 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from openai) (4.14.0)
Requirement already satisfied: idna>=2.8 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from anyio<5,>=3.5.0->openai) (3.10)
Requirement already satisfied: certifi in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx<1,>=0.23.0->openai) (2025.4.26)
Requirement already satisfied: httpcore==1.* in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpx<1,>=0.23.0->openai) (1.0.7)
Requirement already satisfied: h11<0.15,>=0.13 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.14.0)
Requirement already satisfied: annotated-types>=0.6.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pydantic<3,>=1.9.0->openai) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from pydantic<3,>=1.9.0->openai) (0.4.0)
Requirement already satisfied: colorama in c:\users\<USER>\appdata\roaming\python\python313\site-packages (from tqdm>4->openai) (0.4.6)
Downloading openai-1.93.0-py3-none-any.whl (755 kB)
   ---------------------------------------- 755.0/755.0 kB 348.3 kB/s eta 0:00:00
Installing collected packages: openai
  Attempting uninstall: openai
    Found existing installation: openai 1.51.2
    Uninstalling openai-1.51.2:
      Successfully uninstalled openai-1.51.2
Successfully installed openai-1.93.0
