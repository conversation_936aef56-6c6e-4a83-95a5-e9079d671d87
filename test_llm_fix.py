#!/usr/bin/env python3
"""
Test script to verify the LLM API fixes.
This script tests the OpenAI client initialization and API calls.
"""

import os
import sys
import logging

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_openai_import():
    """Test if OpenAI package can be imported correctly."""
    try:
        from openai import OpenAI
        logging.info("✓ OpenAI package imported successfully")
        return True
    except ImportError as e:
        logging.error(f"✗ Failed to import OpenAI package: {e}")
        return False

def test_client_initialization():
    """Test OpenAI client initialization with Gemini configuration."""
    try:
        from openai import OpenAI

        # Test with dummy API key
        api_key = "test-api-key"
        base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"

        try:
            client = OpenAI(
                api_key=api_key,
                base_url=base_url
            )
            logging.info("✓ OpenAI client initialized successfully")
            return True
        except TypeError as te:
            if "proxies" in str(te):
                logging.warning("⚠ OpenAI client doesn't support 'proxies' parameter, but this is handled")
                # Try again without proxies (this should work)
                client = OpenAI(
                    api_key=api_key,
                    base_url=base_url
                )
                logging.info("✓ OpenAI client initialized successfully after handling proxies error")
                return True
            else:
                logging.error(f"✗ Unexpected TypeError during client initialization: {te}")
                return False
    except Exception as e:
        logging.error(f"✗ Failed to initialize OpenAI client: {e}")
        return False

def test_llm_response_generator():
    """Test the LLM response generator module."""
    try:
        # Test the client creation function directly
        from game.llm_response_generator_openai_format import _create_openai_client

        # Test with a dummy API key
        api_key = "test-api-key"

        try:
            client = _create_openai_client(api_key)
            logging.info("✓ OpenAI client creation function works correctly")
            return True
        except Exception as e:
            if "Failed to initialize OpenAI client" in str(e):
                logging.info("✓ Client creation function handled the error gracefully")
                return True
            else:
                logging.error(f"✗ Unexpected error in client creation: {e}")
                return False

    except Exception as e:
        logging.error(f"✗ Error testing LLM response generator: {e}")
        return False

def test_client_creation_functions():
    """Test all client creation functions."""
    try:
        # Test response generator client creation
        from game.llm_response_generator_openai_format import _create_openai_client
        from game.guideline_evaluation import _create_openai_client_for_evaluation
        from game.llm_feedback_generator import _create_openai_client_for_feedback

        api_key = "test-api-key"

        functions_to_test = [
            ("Response Generator", _create_openai_client),
            ("Guideline Evaluation", _create_openai_client_for_evaluation),
            ("Feedback Generator", _create_openai_client_for_feedback),
        ]

        all_passed = True
        for name, func in functions_to_test:
            try:
                client = func(api_key)
                logging.info(f"✓ {name} client creation works")
            except Exception as e:
                if "Failed to initialize OpenAI client" in str(e):
                    logging.info(f"✓ {name} client creation handled error gracefully")
                else:
                    logging.error(f"✗ {name} client creation failed: {e}")
                    all_passed = False

        return all_passed

    except Exception as e:
        logging.error(f"✗ Error testing client creation functions: {e}")
        return False

def main():
    """Run all tests."""
    logging.info("Starting LLM API fix tests...")

    tests = [
        ("OpenAI Import", test_openai_import),
        ("Client Initialization", test_client_initialization),
        ("LLM Response Generator", test_llm_response_generator),
        ("Client Creation Functions", test_client_creation_functions),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logging.info(f"\n--- Testing {test_name} ---")
        if test_func():
            passed += 1
        else:
            logging.error(f"Test {test_name} failed")

    logging.info(f"\n--- Test Results ---")
    logging.info(f"Passed: {passed}/{total}")

    if passed == total:
        logging.info("🎉 All tests passed! The LLM API fixes appear to be working.")
        return 0
    else:
        logging.error("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
